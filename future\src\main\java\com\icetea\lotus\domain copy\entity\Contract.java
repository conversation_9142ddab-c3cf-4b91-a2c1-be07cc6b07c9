package com.icetea.lotus.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Temporary stub for Contract entity
 * TODO: Replace with import from future-core when available
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Contract {
    private String symbol;
    private String name;
    private String baseAsset;
    private String quoteAsset;
    private BigDecimal contractSize;
    private BigDecimal tickSize;
    private BigDecimal minOrderSize;
    private BigDecimal maxOrderSize;
    private BigDecimal makerFeeRate;
    private BigDecimal takerFeeRate;
    private boolean enabled;
}
