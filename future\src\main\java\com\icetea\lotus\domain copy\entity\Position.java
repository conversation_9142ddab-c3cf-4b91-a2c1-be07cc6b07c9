package com.icetea.lotus.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Temporary stub for Position entity
 * TODO: Replace with import from future-core when available
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Position {
    private String id;
    private Long memberId;
    private String symbol;
    private PositionDirection direction;
    private BigDecimal volume;
    private BigDecimal entryPrice;
    private BigDecimal markPrice;
    private BigDecimal liquidationPrice;
    private BigDecimal unrealizedPnl;
    private BigDecimal realizedPnl;
    private BigDecimal margin;
    private BigDecimal leverage;
    private PositionStatus status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    public enum PositionDirection {
        LONG, SHORT
    }
    
    public enum PositionStatus {
        OPEN, CLOSED, LIQUIDATED
    }
}
