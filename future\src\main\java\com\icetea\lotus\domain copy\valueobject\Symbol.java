package com.icetea.lotus.domain.valueobject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Temporary stub for Symbol value object
 * TODO: Replace with import from future-core when available
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Symbol {
    private String value;
    
    public static Symbol of(String value) {
        return new Symbol(value);
    }
}
