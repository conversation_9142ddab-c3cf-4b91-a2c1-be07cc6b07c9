package com.icetea.lotus.domain.valueobject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Temporary stub for Money value object
 * TODO: Replace with import from future-core when available
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Money {
    private BigDecimal amount;
    private String currency;
    
    public static Money of(BigDecimal amount, String currency) {
        return new Money(amount, currency);
    }
    
    public static Money of(String amount, String currency) {
        return new Money(new BigDecimal(amount), currency);
    }
}
