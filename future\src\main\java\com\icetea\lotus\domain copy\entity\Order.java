package com.icetea.lotus.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Temporary stub for Order entity
 * TODO: Replace with import from future-core when available
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Order {
    private String id;
    private Long memberId;
    private String symbol;
    private OrderDirection direction;
    private OrderType type;
    private BigDecimal price;
    private BigDecimal volume;
    private BigDecimal filledVolume;
    private OrderStatus status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    public enum OrderDirection {
        BUY, SELL
    }
    
    public enum OrderType {
        MARKET, LIMIT, STOP, STOP_LIMIT
    }
    
    public enum OrderStatus {
        PENDING, PARTIAL_FILLED, FILLED, CANCELLED
    }
}
