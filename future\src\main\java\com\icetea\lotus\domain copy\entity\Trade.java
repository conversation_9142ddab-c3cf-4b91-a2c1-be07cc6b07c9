package com.icetea.lotus.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Temporary stub for Trade entity
 * TODO: Replace with import from future-core when available
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Trade {
    private String id;
    private String symbol;
    private String buyOrderId;
    private String sellOrderId;
    private Long buyMemberId;
    private Long sellMemberId;
    private BigDecimal price;
    private BigDecimal volume;
    private BigDecimal fee;
    private LocalDateTime createdAt;
}
