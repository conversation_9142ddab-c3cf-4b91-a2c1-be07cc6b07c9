package com.icetea.lotus.domain.valueobject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Temporary stub for OrderId value object
 * TODO: Replace with import from future-core when available
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderId {
    private String value;
    
    public static OrderId of(String value) {
        return new OrderId(value);
    }
}
