server:
  port: 8082

spring:
  application:
    name: future-api-service
  profiles:
    active: dev

  # Database Configuration
  datasource:
    url: ******************************************
    username: ${DB_USERNAME:future_user}
    password: ${DB_PASSWORD:future_pass}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  jpa:
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        show_sql: false
    open-in-view: false

  # Redis Configuration
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 1
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5

  # Kafka Configuration
  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:9092}
    consumer:
      group-id: future-api
      auto-offset-reset: latest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        spring.json.trusted.packages: "com.icetea.lotus"
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer

  # Cloud Configuration
  cloud:
    consul:
      host: ${CONSUL_HOST:localhost}
      port: ${CONSUL_PORT:8500}
      discovery:
        service-name: ${spring.application.name}
        health-check-path: /actuator/health
        health-check-interval: 10s

# Service Integration Configuration
services:
  future-service:
    url: http://future-service:8083
    timeout: 5000ms
  market-service:
    url: http://market-service:8080
    timeout: 3000ms
  wallet-service:
    url: http://wallet-service:8081
    timeout: 3000ms

# Circuit Breaker Configuration
resilience4j:
  circuitbreaker:
    instances:
      future-service:
        registerHealthIndicator: true
        slidingWindowSize: 10
        minimumNumberOfCalls: 5
        permittedNumberOfCallsInHalfOpenState: 3
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 5s
        failureRateThreshold: 50
      market-service:
        registerHealthIndicator: true
        slidingWindowSize: 10
        minimumNumberOfCalls: 5
        failureRateThreshold: 50

# Kafka Topics
kafka:
  topics:
    order-commands: future-order-commands
    order-events: future-order-events
    trade-events: future-trade-events
    position-updates: future-position-updates

# WebSocket Configuration
websocket:
  allowed-origins: "*"
  endpoint: "/ws"
  topic-prefix: "/topic"
  app-prefix: "/app"

# Notification Configuration
notification:
  email:
    enabled: true
    smtp:
      host: ${SMTP_HOST:localhost}
      port: ${SMTP_PORT:587}
      username: ${SMTP_USERNAME:}
      password: ${SMTP_PASSWORD:}
  sms:
    enabled: true
    provider: ${SMS_PROVIDER:twilio}

# OpenAPI Configuration
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method

# Logging Configuration
logging:
  level:
    com.icetea.lotus: INFO
    org.springframework.kafka: WARN
    org.springframework.web: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Management Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
